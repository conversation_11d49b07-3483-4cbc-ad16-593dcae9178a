"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateComment = void 0;
const prismaClient_1 = __importDefault(require("../../../../utils/prismaClient"));
const helpers_1 = require("../../../../utils/helpers");
const updateComment = async (req, res) => {
    try {
        const { id: commentId } = req.params;
        const { content, updatedBy } = req.body;
        if (!commentId || !content) {
            return res.status(400).json({
                success: false,
                message: "Comment ID and content are required",
            });
        }
        // Only use updatedBy from req.body or fallback to 'system'
        let username = updatedBy || "system";
        const comment = await prismaClient_1.default.comment.update({
            where: { id: commentId },
            data: {
                content,
                updatedBy: username,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Comment updated successfully",
            data: comment,
        });
    }
    catch (error) {
        console.error("Error updating comment:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.updateComment = updateComment;
//# sourceMappingURL=update.js.map
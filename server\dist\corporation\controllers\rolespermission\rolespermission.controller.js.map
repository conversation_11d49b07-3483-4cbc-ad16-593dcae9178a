{"version": 3, "file": "rolespermission.controller.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/rolespermission/rolespermission.controller.ts"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,mCAAmC;AACnC,WAAW;AACX,sBAAsB;AACtB,oBAAoB;AACpB,0CAA0C;AAC1C,yDAAyD;AAEzD,wEAAwE;AACxE,UAAU;AACV,6CAA6C;AAC7C,uDAAuD;AACvD,2EAA2E;AAC3E,wEAAwE;AACxE,iDAAiD;AAEjD,kCAAkC;AAClC,uEAAuE;AACvE,mDAAmD;AAEnD,iCAAiC;AACjC,yBAAyB;AACzB,kCAAkC;AAClC,wBAAwB;AACxB,SAAS;AACT,kDAAkD;AAClD,uDAAuD;AACvD,iBAAiB;AACjB,kBAAkB;AAClB,aAAa;AACb,qBAAqB;AACrB,SAAS;AACT,qDAAqD;AAErD,yBAAyB;AACzB,iCAAiC;AACjC,gDAAgD;AAChD,gCAAgC;AAChC,kBAAkB;AAClB,qBAAqB;AACrB,sCAAsC;AACtC,4BAA4B;AAC5B,aAAa;AACb,YAAY;AACZ,qCAAqC;AACrC,6BAA6B;AAC7B,qDAAqD;AACrD,+CAA+C;AAC/C,wBAAwB;AACxB,iDAAiD;AACjD,uCAAuC;AACvC,2BAA2B;AAC3B,mBAAmB;AACnB,kBAAkB;AAClB,eAAe;AACf,aAAa;AACb,UAAU;AACV,UAAU;AAEV,qBAAqB;AACrB,wBAAwB;AACxB,mBAAmB;AACnB,gBAAgB;AAChB,mBAAmB;AACnB,iBAAiB;AACjB,2CAA2C;AAC3C,SAAS;AACT,+EAA+E;AAC/E,sBAAsB;AACtB,0BAA0B;AAC1B,MAAM;AACN,KAAK;AAEL,kEAAkE;AAClE,aAAa;AACb,2CAA2C;AAC3C,mBAAmB;AACnB,iCAAiC;AACjC,4CAA4C;AAC5C,cAAc;AACd,aAAa;AACb,2CAA2C;AAC3C,mBAAmB;AACnB,iCAAiC;AACjC,yCAAyC;AACzC,cAAc;AACd,aAAa;AACb,uCAAuC;AACvC,0BAA0B;AAC1B,2CAA2C;AAC3C,aAAa;AACb,0BAA0B;AAC1B,uCAAuC;AACvC,2BAA2B;AAC3B,6DAA6D;AAC7D,aAAa;AACb,SAAS;AACT,KAAK"}
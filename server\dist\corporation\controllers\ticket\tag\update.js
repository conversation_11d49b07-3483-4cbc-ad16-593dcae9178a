"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateTag = void 0;
const prismaClient_1 = __importDefault(require("../../../../utils/prismaClient"));
const helpers_1 = require("../../../../utils/helpers");
const updateTag = async (req, res) => {
    try {
        const { id: tagId } = req.params;
        const { tagName, color, updatedBy } = req.body;
        if (!tagId) {
            return res.status(400).json({
                success: false,
                message: "Tag ID is required",
            });
        }
        // Check if tag exists
        const existingTag = await prismaClient_1.default.tag.findFirst({
            where: { id: tagId, deletedAt: null },
        });
        if (!existingTag) {
            return res.status(404).json({
                success: false,
                message: "Tag not found",
            });
        }
        // Check if tag name already exists (case insensitive) if updating name
        if (tagName && tagName !== existingTag.tagName) {
            const duplicateTag = await prismaClient_1.default.tag.findFirst({
                where: {
                    tagName: {
                        equals: tagName.trim(),
                        mode: 'insensitive'
                    },
                    id: { not: tagId },
                    deletedAt: null,
                },
            });
            if (duplicateTag) {
                return res.status(409).json({
                    success: false,
                    message: "A tag with this name already exists",
                });
            }
        }
        // Use updatedBy from req.body or fallback to 'system'
        let username = updatedBy || "system";
        const updateData = {
            updatedBy: username,
        };
        if (tagName)
            updateData.tagName = tagName.trim();
        if (color)
            updateData.color = color;
        const tag = await prismaClient_1.default.tag.update({
            where: { id: tagId },
            data: updateData,
        });
        return res.status(200).json({
            success: true,
            message: "Tag updated successfully",
            data: tag,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.updateTag = updateTag;
//# sourceMappingURL=update.js.map
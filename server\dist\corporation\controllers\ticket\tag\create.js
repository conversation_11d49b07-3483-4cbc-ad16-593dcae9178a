"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTag = void 0;
const prismaClient_1 = __importDefault(require("../../../../utils/prismaClient"));
const helpers_1 = require("../../../../utils/helpers");
const createTag = async (req, res) => {
    try {
        const { tagName, color, createdBy } = req.body;
        if (!tagName || !color) {
            return res.status(400).json({
                success: false,
                message: "Tag name and color are required",
            });
        }
        // Check if tag name already exists (case insensitive)
        const existingTag = await prismaClient_1.default.tag.findFirst({
            where: {
                tagName: {
                    equals: tagName.trim(),
                    mode: 'insensitive'
                },
                deletedAt: null,
            },
        });
        if (existingTag) {
            return res.status(409).json({
                success: false,
                message: "A tag with this name already exists",
            });
        }
        // Use createdBy from req.body or fallback to 'system'
        let username = createdBy || "system";
        const tag = await prismaClient_1.default.tag.create({
            data: {
                tagName: tagName.trim(),
                color,
                createdBy: username,
            },
        });
        return res.status(201).json({
            success: true,
            message: "Tag created successfully",
            data: tag,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createTag = createTag;
//# sourceMappingURL=create.js.map